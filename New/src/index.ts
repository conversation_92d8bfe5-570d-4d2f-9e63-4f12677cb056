import { Hono } from "hono";
import { contextStorage } from "hono/context-storage";
import { handleWebhookEvent } from "./webhook/handler";
import { getBufferStats } from "./utils/bufferManager";

const app = new Hono<Env>();
app.use(contextStorage());

app.onError((err, c) => {
  console.error(err);
  return c.json(
    {
      message: "Internal Server Error",
      requestId: crypto.randomUUID(),
      timestamp: new Date().toISOString(),
    },
    500
  );
});

// Health check endpoints
app.get("/", (c) => c.text("DermaCare Data Sync Service - OK"));
app.get("/health", (c) =>
  c.json({
    status: "healthy",
    timestamp: new Date().toISOString(),
    version: "2.0.0",
    bufferStats: getBufferStats(),
  })
);

// Webhook endpoint for receiving data synchronization events
// Replaces socket.io events with HTTP webhooks
app.post("/webhook", handleWebhookEvent);

// Legacy webhook endpoint for backward compatibility
app.post("/webhook/sync", handleWebhookEvent);

export default app;
