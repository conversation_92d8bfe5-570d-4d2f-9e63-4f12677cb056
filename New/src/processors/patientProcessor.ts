import type { GetCCPatientType, WebhookContext } from "@type";
import { db } from "@database";
import { patient } from "@database/schema";
import { eq, and } from "drizzle-orm";
import { apClient } from "@api/apClient";
import { logError, logSyncError } from "@utils/errorLogger";
import { generatePatientBufferKey, checkAndAddToBuffer } from "@utils/bufferManager";

/**
 * Processes patient creation events from CC
 * Equivalent to ProcessPatientCreate job from v3Integration
 * 
 * @param payload - Patient data from CC webhook
 * @param context - Webhook processing context
 * @returns Processing result
 */
export async function processPatientCreate(
  payload: GetCCPatientType,
  context: WebhookContext
): Promise<{ success: boolean; message: string; patientId?: string }> {
  const { requestId } = context;
  
  try {
    console.log(`Processing patient create for CC ID: ${payload.id}`);
    
    // Check buffer to prevent duplicate processing
    const bufferKey = generatePatientBufferKey("ProcessPatientCreate", payload.id);
    if (checkAndAddToBuffer(bufferKey)) {
      return {
        success: true,
        message: `Patient creation recently processed, skipping. CC ID: ${payload.id}`,
      };
    }

    // Check if patient already exists in our database
    const existingPatient = await db
      .select()
      .from(patient)
      .where(eq(patient.ccId, payload.id))
      .limit(1);

    let dbPatient = existingPatient[0];

    if (dbPatient && dbPatient.apId) {
      console.log(`Patient already exists in AP, ID: ${dbPatient.apId}`);
      return {
        success: true,
        message: `Patient already exists in AP, ID: ${dbPatient.apId}`,
        patientId: dbPatient.apId,
      };
    }

    // Validate required data
    if (!payload.email && !payload.phoneMobile) {
      const message = `Email and phone are empty, dropping create patient request. CC ID: ${payload.id}`;
      console.log(message);
      return {
        success: false,
        message,
      };
    }

    // Create or update patient record in database
    if (!dbPatient) {
      const [newPatient] = await db
        .insert(patient)
        .values({
          ccId: payload.id,
          email: payload.email || null,
          phone: payload.phoneMobile || null,
          ccData: payload,
          ccUpdatedAt: new Date(payload.updatedAt),
        })
        .returning();
      dbPatient = newPatient;
    } else {
      // Update existing patient with new CC data
      const [updatedPatient] = await db
        .update(patient)
        .set({
          email: payload.email || null,
          phone: payload.phoneMobile || null,
          ccData: payload,
          ccUpdatedAt: new Date(payload.updatedAt),
          updatedAt: new Date(),
        })
        .where(eq(patient.id, dbPatient.id))
        .returning();
      dbPatient = updatedPatient;
    }

    // Create contact in AP
    const apContactResult = await createOrUpdateAPContact(dbPatient, payload);
    
    if (apContactResult.success && apContactResult.apContact) {
      // Update database with AP data
      await db
        .update(patient)
        .set({
          apId: apContactResult.apContact.id,
          apData: apContactResult.apContact,
          apUpdatedAt: new Date(apContactResult.apContact.dateUpdated || new Date().toISOString()),
          updatedAt: new Date(),
        })
        .where(eq(patient.id, dbPatient.id));

      console.log(`Patient created successfully in AP. CC ID: ${payload.id}, AP ID: ${apContactResult.apContact.id}`);
      
      return {
        success: true,
        message: `Patient created successfully in AP. CC ID: ${payload.id}, AP ID: ${apContactResult.apContact.id}`,
        patientId: apContactResult.apContact.id,
      };
    } else {
      await logSyncError(
        "PATIENT_CREATE_AP_FAILED",
        new Error(apContactResult.message),
        payload.id,
        "CC",
        "PatientProcessor"
      );
      
      return {
        success: false,
        message: `Failed to create patient in AP: ${apContactResult.message}`,
      };
    }

  } catch (error) {
    await logSyncError(
      "PATIENT_CREATE_ERROR",
      error,
      payload.id,
      "CC",
      "PatientProcessor"
    );
    
    throw error;
  }
}

/**
 * Processes patient update events from CC
 * Equivalent to ProcessPatientUpdate job from v3Integration
 * 
 * @param payload - Updated patient data from CC webhook
 * @param context - Webhook processing context
 * @returns Processing result
 */
export async function processPatientUpdate(
  payload: GetCCPatientType,
  context: WebhookContext
): Promise<{ success: boolean; message: string; patientId?: string }> {
  const { requestId } = context;
  
  try {
    console.log(`Processing patient update for CC ID: ${payload.id}`);
    
    // Check buffer to prevent duplicate processing
    const bufferKey = generatePatientBufferKey("ProcessPatientUpdate", payload.id);
    if (checkAndAddToBuffer(bufferKey)) {
      return {
        success: true,
        message: `Patient update recently processed, skipping. CC ID: ${payload.id}`,
      };
    }

    // Validate required data
    if (!payload.email && !payload.phoneMobile) {
      const message = `Email and phone are empty, dropping update patient request. CC ID: ${payload.id}`;
      console.log(message);
      return {
        success: false,
        message,
      };
    }

    // Find or create patient in database
    let dbPatient = await db
      .select()
      .from(patient)
      .where(eq(patient.ccId, payload.id))
      .limit(1)
      .then(results => results[0]);

    if (!dbPatient) {
      // Patient doesn't exist, create it
      const [newPatient] = await db
        .insert(patient)
        .values({
          ccId: payload.id,
          email: payload.email || null,
          phone: payload.phoneMobile || null,
          ccData: payload,
          ccUpdatedAt: new Date(payload.updatedAt),
        })
        .returning();
      dbPatient = newPatient;
    } else {
      // Update existing patient
      const [updatedPatient] = await db
        .update(patient)
        .set({
          email: payload.email || null,
          phone: payload.phoneMobile || null,
          ccData: payload,
          ccUpdatedAt: new Date(payload.updatedAt),
          updatedAt: new Date(),
        })
        .where(eq(patient.id, dbPatient.id))
        .returning();
      dbPatient = updatedPatient;
    }

    // Create or update contact in AP
    const apContactResult = await createOrUpdateAPContact(dbPatient, payload);
    
    if (apContactResult.success && apContactResult.apContact) {
      // Update database with AP data
      await db
        .update(patient)
        .set({
          apId: apContactResult.apContact.id,
          apData: apContactResult.apContact,
          apUpdatedAt: new Date(apContactResult.apContact.dateUpdated || new Date().toISOString()),
          updatedAt: new Date(),
        })
        .where(eq(patient.id, dbPatient.id));

      console.log(`Patient updated successfully in AP. CC ID: ${payload.id}, AP ID: ${apContactResult.apContact.id}`);
      
      return {
        success: true,
        message: `Patient updated successfully in AP. CC ID: ${payload.id}, AP ID: ${apContactResult.apContact.id}`,
        patientId: apContactResult.apContact.id,
      };
    } else {
      await logSyncError(
        "PATIENT_UPDATE_AP_FAILED",
        new Error(apContactResult.message),
        payload.id,
        "CC",
        "PatientProcessor"
      );
      
      return {
        success: false,
        message: `Failed to update patient in AP: ${apContactResult.message}`,
      };
    }

  } catch (error) {
    await logSyncError(
      "PATIENT_UPDATE_ERROR",
      error,
      payload.id,
      "CC",
      "PatientProcessor"
    );
    
    throw error;
  }
}

/**
 * Creates or updates a contact in AP based on patient data
 * 
 * @param dbPatient - Patient record from database
 * @param ccPatient - Patient data from CC
 * @returns Result of AP contact operation
 */
async function createOrUpdateAPContact(
  dbPatient: typeof patient.$inferSelect,
  ccPatient: GetCCPatientType
): Promise<{ success: boolean; message: string; apContact?: any }> {
  try {
    const contactData = {
      email: ccPatient.email || null,
      phone: ccPatient.phoneMobile || null,
      firstName: ccPatient.firstName || null,
      lastName: ccPatient.lastName || null,
      dateOfBirth: ccPatient.dob || null,
      gender: ccPatient.gender || null,
      tags: ["cc_api"],
      source: "cc" as const,
    };

    let apContact;
    
    if (dbPatient.apId) {
      // Update existing contact
      console.log(`Updating existing AP contact: ${dbPatient.apId}`);
      apContact = await apClient.contact.update(dbPatient.apId, contactData);
    } else {
      // Create new contact using upsert to handle duplicates
      console.log(`Creating new AP contact for CC patient: ${ccPatient.id}`);
      apContact = await apClient.contact.upsert(contactData);
    }

    return {
      success: true,
      message: "Contact created/updated successfully in AP",
      apContact,
    };

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.error(`Failed to create/update AP contact:`, error);
    
    return {
      success: false,
      message: `Failed to create/update AP contact: ${errorMessage}`,
    };
  }
}
