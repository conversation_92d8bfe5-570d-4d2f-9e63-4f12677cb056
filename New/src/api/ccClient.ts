import type { 
  GetCCPatientType, 
  PostCCPatientType, 
  GetCCAppointmentType, 
  PostCCAppointmentType,
  GetCCCustomField,
  GetCCLocationType,
  GetCCResourceType,
  GetCCUserType,
  GetInvoiceType,
  GetPaymentType
} from "@type";
import { createApiClient, extractResponseData, type RequestOptions, type ApiResponse } from "./request";
import { getConfig } from "@utils/configs";
import { logError } from "@utils/errorLogger";

/**
 * CC (CliniCore) API client
 * Handles all communication with the CliniCore API
 */
class CCApiClient {
  private client: ReturnType<typeof createApiClient>;

  constructor() {
    const ccApiDomain = getConfig("ccApiDomain") as string;
    const ccApiKey = getConfig("ccApiKey");

    this.client = createApiClient(ccApiDomain, {
      "Authorization": `Bearer ${ccApiKey}`,
    });
  }

  /**
   * Patient operations
   */
  patient = {
    /**
     * Create a new patient
     * @param data - Patient data
     * @returns Created patient
     */
    create: async (data: PostCCPatientType): Promise<GetCCPatientType> => {
      const response = await this.client<{ patient: GetCCPatientType }>({
        url: "/patients",
        method: "POST",
        data: { patient: data },
      });
      return extractResponseData(response, "patient");
    },

    /**
     * Update a patient
     * @param id - Patient ID
     * @param data - Patient data to update
     * @returns Updated patient
     */
    update: async (id: number, data: PostCCPatientType): Promise<GetCCPatientType> => {
      const response = await this.client<{ patient: GetCCPatientType }>({
        url: `/patients/${id}`,
        method: "PUT",
        data: { patient: data },
      });
      return extractResponseData(response, "patient");
    },

    /**
     * Get a patient by ID
     * @param id - Patient ID
     * @returns Patient data
     */
    get: async (id: number): Promise<GetCCPatientType> => {
      const response = await this.client<{ patient: GetCCPatientType }>({
        url: `/patients/${id}`,
        method: "GET",
      });
      return extractResponseData(response, "patient");
    },

    /**
     * Search patients
     * @param params - Search parameters
     * @returns Array of patients
     */
    search: async (params: {
      email?: string;
      phone?: string;
      firstName?: string;
      lastName?: string;
      limit?: number;
      offset?: number;
    }): Promise<GetCCPatientType[]> => {
      const response = await this.client<{ patients: GetCCPatientType[] }>({
        url: "/patients",
        method: "GET",
        params,
      });
      return extractResponseData(response, "patients");
    },

    /**
     * Get all patients
     * @param params - Query parameters
     * @returns Array of patients
     */
    all: async (params?: {
      limit?: number;
      offset?: number;
      search?: string;
    }): Promise<GetCCPatientType[]> => {
      const response = await this.client<{ patients: GetCCPatientType[] }>({
        url: "/patients",
        method: "GET",
        params,
      });
      return extractResponseData(response, "patients");
    },

    /**
     * Delete a patient
     * @param id - Patient ID
     */
    delete: async (id: number): Promise<void> => {
      await this.client({
        url: `/patients/${id}`,
        method: "DELETE",
      });
    },
  };

  /**
   * Appointment operations
   */
  appointment = {
    /**
     * Create a new appointment
     * @param data - Appointment data
     * @returns Created appointment
     */
    create: async (data: PostCCAppointmentType): Promise<GetCCAppointmentType> => {
      const response = await this.client<{ appointment: GetCCAppointmentType }>({
        url: "/appointments",
        method: "POST",
        data: { appointment: data },
      });
      return extractResponseData(response, "appointment");
    },

    /**
     * Update an appointment
     * @param id - Appointment ID
     * @param data - Appointment data to update
     * @returns Updated appointment
     */
    update: async (id: number, data: Partial<PostCCAppointmentType>): Promise<GetCCAppointmentType> => {
      const response = await this.client<{ appointment: GetCCAppointmentType }>({
        url: `/appointments/${id}`,
        method: "PUT",
        data: { appointment: data },
      });
      return extractResponseData(response, "appointment");
    },

    /**
     * Get an appointment by ID
     * @param id - Appointment ID
     * @returns Appointment data
     */
    get: async (id: number): Promise<GetCCAppointmentType> => {
      const response = await this.client<{ appointment: GetCCAppointmentType }>({
        url: `/appointments/${id}`,
        method: "GET",
      });
      return extractResponseData(response, "appointment");
    },

    /**
     * Cancel an appointment
     * @param id - Appointment ID
     * @param reason - Cancellation reason
     * @returns Updated appointment
     */
    cancel: async (id: number, reason?: string): Promise<GetCCAppointmentType> => {
      const response = await this.client<{ appointment: GetCCAppointmentType }>({
        url: `/appointments/${id}`,
        method: "PUT",
        data: { 
          appointment: { 
            canceledWhy: reason || "Cancelled via API" 
          } 
        },
      });
      return extractResponseData(response, "appointment");
    },

    /**
     * Delete an appointment
     * @param id - Appointment ID
     */
    delete: async (id: number): Promise<void> => {
      await this.client({
        url: `/appointments/${id}`,
        method: "DELETE",
      });
    },

    /**
     * Get appointments for a patient
     * @param patientId - Patient ID
     * @returns Array of appointments
     */
    getByPatient: async (patientId: number): Promise<GetCCAppointmentType[]> => {
      const response = await this.client<{ appointments: GetCCAppointmentType[] }>({
        url: "/appointments",
        method: "GET",
        params: { patient: patientId.toString() },
      });
      return extractResponseData(response, "appointments");
    },
  };

  /**
   * Invoice operations
   */
  invoice = {
    /**
     * Get an invoice by ID
     * @param id - Invoice ID
     * @returns Invoice data
     */
    get: async (id: number): Promise<GetInvoiceType> => {
      const response = await this.client<{ invoice: GetInvoiceType }>({
        url: `/invoices/${id}`,
        method: "GET",
      });
      return extractResponseData(response, "invoice");
    },

    /**
     * Get invoices by IDs
     * @param ids - Array of invoice IDs
     * @returns Array of invoices
     */
    getMultiple: async (ids: number[]): Promise<GetInvoiceType[]> => {
      const promises = ids.map(id => this.invoice.get(id));
      return Promise.all(promises);
    },

    /**
     * Get invoices for a patient
     * @param patientId - Patient ID
     * @returns Array of invoices
     */
    getByPatient: async (patientId: number): Promise<GetInvoiceType[]> => {
      const response = await this.client<{ invoices: GetInvoiceType[] }>({
        url: "/invoices",
        method: "GET",
        params: { patient: patientId.toString() },
      });
      return extractResponseData(response, "invoices");
    },
  };

  /**
   * Payment operations
   */
  payment = {
    /**
     * Get a payment by ID
     * @param id - Payment ID
     * @returns Payment data
     */
    get: async (id: number): Promise<GetPaymentType> => {
      const response = await this.client<{ payment: GetPaymentType }>({
        url: `/payments/${id}`,
        method: "GET",
      });
      return extractResponseData(response, "payment");
    },

    /**
     * Get payments by IDs
     * @param ids - Array of payment IDs
     * @returns Array of payments
     */
    getMultiple: async (ids: number[]): Promise<GetPaymentType[]> => {
      const promises = ids.map(id => this.payment.get(id));
      return Promise.all(promises);
    },

    /**
     * Get payments for a patient
     * @param patientId - Patient ID
     * @returns Array of payments
     */
    getByPatient: async (patientId: number): Promise<GetPaymentType[]> => {
      const response = await this.client<{ payments: GetPaymentType[] }>({
        url: "/payments",
        method: "GET",
        params: { patient: patientId.toString() },
      });
      return extractResponseData(response, "payments");
    },
  };

  /**
   * Custom field operations
   */
  customField = {
    /**
     * Get all custom fields
     * @returns Array of custom fields
     */
    all: async (): Promise<GetCCCustomField[]> => {
      const response = await this.client<{ customFields: GetCCCustomField[] }>({
        url: "/custom-fields",
        method: "GET",
      });
      return extractResponseData(response, "customFields");
    },

    /**
     * Get a custom field by ID
     * @param id - Custom field ID
     * @returns Custom field data
     */
    get: async (id: number): Promise<GetCCCustomField> => {
      const response = await this.client<{ customField: GetCCCustomField }>({
        url: `/custom-fields/${id}`,
        method: "GET",
      });
      return extractResponseData(response, "customField");
    },
  };

  /**
   * Location operations
   */
  location = {
    /**
     * Get all locations
     * @returns Array of locations
     */
    all: async (): Promise<GetCCLocationType[]> => {
      const response = await this.client<{ locations: GetCCLocationType[] }>({
        url: "/locations",
        method: "GET",
      });
      return extractResponseData(response, "locations");
    },

    /**
     * Get a location by ID
     * @param id - Location ID
     * @returns Location data
     */
    get: async (id: number): Promise<GetCCLocationType> => {
      const response = await this.client<{ location: GetCCLocationType }>({
        url: `/locations/${id}`,
        method: "GET",
      });
      return extractResponseData(response, "location");
    },
  };

  /**
   * Resource operations
   */
  resource = {
    /**
     * Get all resources
     * @returns Array of resources
     */
    all: async (): Promise<GetCCResourceType[]> => {
      const response = await this.client<{ resources: GetCCResourceType[] }>({
        url: "/resources",
        method: "GET",
      });
      return extractResponseData(response, "resources");
    },

    /**
     * Get a resource by ID
     * @param id - Resource ID
     * @returns Resource data
     */
    get: async (id: number): Promise<GetCCResourceType> => {
      const response = await this.client<{ resource: GetCCResourceType }>({
        url: `/resources/${id}`,
        method: "GET",
      });
      return extractResponseData(response, "resource");
    },
  };

  /**
   * User operations
   */
  user = {
    /**
     * Get all users
     * @returns Array of users
     */
    all: async (): Promise<GetCCUserType[]> => {
      const response = await this.client<{ users: GetCCUserType[] }>({
        url: "/users",
        method: "GET",
      });
      return extractResponseData(response, "users");
    },

    /**
     * Get a user by ID
     * @param id - User ID
     * @returns User data
     */
    get: async (id: number): Promise<GetCCUserType> => {
      const response = await this.client<{ user: GetCCUserType }>({
        url: `/users/${id}`,
        method: "GET",
      });
      return extractResponseData(response, "user");
    },
  };
}

// Export singleton instance
export const ccClient = new CCApiClient();
