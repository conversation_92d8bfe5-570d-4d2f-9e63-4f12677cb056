import type { GetAPContactType, PostAPContactType, GetAPAppointmentType, PostAPAppointmentType, APGetCustomFieldType, APPostCustomfieldType } from "@type";
import { createApiClient, extractResponseData, type RequestOptions, type ApiResponse } from "./request";
import { getConfig } from "@utils/configs";
import { logError } from "@utils/errorLogger";

/**
 * AP (AutoPatient) API client
 * Handles all communication with the AutoPatient API
 */
class APApiClient {
  private client: ReturnType<typeof createApiClient>;
  private v1Client: ReturnType<typeof createApiClient>;

  constructor() {
    const apApiDomain = getConfig("apApiDomain");
    const apApiKey = getConfig("apApiKey");
    const locationID = getConfig("locationID");

    // V2 API client (services.leadconnectorhq.com)
    this.client = createApiClient(apApiDomain as string, {
      "Authorization": `Bearer ${apApi<PERSON>ey}`,
      "Version": "2021-04-15",
    });

    // V1 API client (rest.gohighlevel.com)
    this.v1Client = createApiClient("https://rest.gohighlevel.com/v1/", {
      "Authorization": `Bearer ${apApiKey}`,
    });
  }

  /**
   * Contact operations
   */
  contact = {
    /**
     * Get a contact by ID
     * @param id - Contact ID
     * @returns Contact data
     */
    get: async (id: string): Promise<GetAPContactType> => {
      const response = await this.client<{ contact: GetAPContactType }>({
        url: `/contacts/${id}`,
        method: "GET",
      });
      return extractResponseData(response, "contact");
    },

    /**
     * Create a new contact
     * @param data - Contact data
     * @returns Created contact
     */
    create: async (data: PostAPContactType): Promise<GetAPContactType> => {
      const locationID = getConfig("locationID");
      const response = await this.client<{ contact: GetAPContactType }>({
        url: "/contacts/",
        method: "POST",
        data: { ...data, locationId: locationID },
      });
      return extractResponseData(response, "contact");
    },

    /**
     * Upsert a contact (create or update)
     * @param data - Contact data
     * @returns Contact data
     */
    upsert: async (data: PostAPContactType): Promise<GetAPContactType> => {
      const locationID = getConfig("locationID");
      const response = await this.client<{ contact: GetAPContactType }>({
        url: "/contacts/upsert/",
        method: "POST",
        data: { ...data, locationId: locationID },
      });
      return extractResponseData(response, "contact");
    },

    /**
     * Update a contact
     * @param id - Contact ID
     * @param data - Contact data to update
     * @returns Updated contact
     */
    update: async (id: string, data: PostAPContactType): Promise<GetAPContactType> => {
      const response = await this.client<{ contact: GetAPContactType }>({
        url: `/contacts/${id}`,
        method: "PUT",
        data,
      });
      return extractResponseData(response, "contact");
    },

    /**
     * Delete a contact
     * @param id - Contact ID
     */
    delete: async (id: string): Promise<void> => {
      await this.client({
        url: `/contacts/${id}/`,
        method: "DELETE",
      });
    },

    /**
     * Get contact appointments
     * @param contactId - Contact ID
     * @returns Appointments data
     */
    appointments: async (contactId: string): Promise<any> => {
      const response = await this.client({
        url: `/contacts/${contactId}/appointments/`,
        method: "GET",
      });
      return response.data;
    },

    /**
     * Get all contacts
     * @param params - Query parameters
     * @returns Array of contacts
     */
    all: async (params?: {
      limit?: number;
      query?: string;
      startAfter?: number;
      startAfterId?: string;
    }): Promise<GetAPContactType[]> => {
      const locationID = getConfig("locationID") as string;
      const response = await this.client<{ contacts: GetAPContactType[] }>({
        url: "/contacts/",
        method: "GET",
        params: { ...params, locationId: locationID  },
      });
      return extractResponseData(response, "contacts");
    },
  };

  /**
   * Appointment operations
   */
  appointment = {
    /**
     * Get an appointment by ID
     * @param apId - Appointment ID
     * @returns Appointment data
     */
    get: async (apId: string): Promise<GetAPAppointmentType> => {
      const response = await this.client<{ appointment: GetAPAppointmentType }>({
        url: `/calendars/events/appointments/${apId}`,
        method: "GET",
      });
      return extractResponseData(response, "appointment");
    },

    /**
     * Create a new appointment
     * @param payload - Appointment data
     * @returns Created appointment
     */
    create: async (payload: PostAPAppointmentType): Promise<GetAPAppointmentType> => {
      const locationID = getConfig("locationID");
      const apCalendarId = getConfig("apCalendarId");
      const response = await this.client<GetAPAppointmentType>({
        url: "/calendars/events/appointments",
        method: "POST",
        data: { 
          calendarId: apCalendarId, 
          ...payload, 
          locationId: locationID 
        },
      });
      return response.data;
    },

    /**
     * Create a block slot
     * @param payload - Block slot data
     * @returns Created block slot
     */
    createBlockSlot: async (payload: PostAPAppointmentType): Promise<GetAPAppointmentType> => {
      const locationID = getConfig("locationID");
      const apCalendarId = getConfig("apCalendarId");
      const response = await this.client<GetAPAppointmentType>({
        url: "/calendars/events/block-slots",
        method: "POST",
        data: { 
          calendarId: apCalendarId, 
          ...payload, 
          locationId: locationID 
        },
      });
      return response.data;
    },

    /**
     * Update an appointment
     * @param apId - Appointment ID
     * @param payload - Appointment data to update
     * @returns Updated appointment
     */
    update: async (apId: string, payload: Partial<PostAPAppointmentType>): Promise<GetAPAppointmentType> => {
      const response = await this.client<{ appointment: GetAPAppointmentType }>({
        url: `/calendars/events/appointments/${apId}`,
        method: "PUT",
        data: payload,
      });
      return extractResponseData(response, "appointment");
    },

    /**
     * Delete an appointment
     * @param apId - Appointment ID
     */
    delete: async (apId: string): Promise<void> => {
      await this.client({
        url: `/calendars/events/appointments/${apId}`,
        method: "DELETE",
      });
    },
  };

  /**
   * Custom field operations
   */
  customField = {
    /**
     * Get all custom fields
     * @returns Array of custom fields
     */
    all: async (): Promise<APGetCustomFieldType[]> => {
      const locationID = getConfig("locationID") as string;
      const response = await this.client<{ customFields: APGetCustomFieldType[] }>({
        url: "/custom-fields/",
        method: "GET",
        params: { locationId: locationID },
      });
      return extractResponseData(response, "customFields");
    },

    /**
     * Create a custom field
     * @param data - Custom field data
     * @returns Created custom field
     */
    create: async (data: APPostCustomfieldType): Promise<APGetCustomFieldType> => {
      const locationID = getConfig("locationID");
      const response = await this.client<{ customField: APGetCustomFieldType }>({
        url: "/custom-fields/",
        method: "POST",
        data: { ...data, locationId: locationID },
      });
      return extractResponseData(response, "customField");
    },

    /**
     * Update a custom field
     * @param id - Custom field ID
     * @param data - Custom field data to update
     * @returns Updated custom field
     */
    update: async (id: string, data: Partial<APPostCustomfieldType>): Promise<APGetCustomFieldType> => {
      const response = await this.client<{ customField: APGetCustomFieldType }>({
        url: `/custom-fields/${id}`,
        method: "PUT",
        data,
      });
      return extractResponseData(response, "customField");
    },

    /**
     * Delete a custom field
     * @param id - Custom field ID
     */
    delete: async (id: string): Promise<void> => {
      await this.client({
        url: `/custom-fields/${id}`,
        method: "DELETE",
      });
    },
  };

  /**
   * Note operations
   */
  note = {
    /**
     * Create a note for a contact
     * @param contactId - Contact ID
     * @param body - Note content
     * @returns Created note
     */
    create: async (contactId: string, body: string): Promise<any> => {
      const response = await this.client({
        url: `/contacts/${contactId}/notes/`,
        method: "POST",
        data: { body },
      });
      return response.data;
    },

    /**
     * Update a note
     * @param contactId - Contact ID
     * @param noteId - Note ID
     * @param body - Updated note content
     * @returns Updated note
     */
    update: async (contactId: string, noteId: string, body: string): Promise<any> => {
      const response = await this.client({
        url: `/contacts/${contactId}/notes/${noteId}`,
        method: "PUT",
        data: { body },
      });
      return response.data;
    },
  };
}

// Export singleton instance
export const apClient = new APApiClient();
