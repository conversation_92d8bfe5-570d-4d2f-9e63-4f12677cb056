/**
 * API module exports
 * Provides centralized access to all API clients and utilities
 */

// Export API clients
export { apClient } from "./apClient";
export { ccClient } from "./ccClient";

// Export request utilities
export {
  makeRequest,
  createApiClient,
  extractResponseData,
  ApiError,
  type RequestOptions,
  type ApiResponse,
} from "./request";

// Re-export for convenience
export const api = {
  ap: () => import("./apClient").then(m => m.apClient),
  cc: () => import("./ccClient").then(m => m.ccClient),
};
