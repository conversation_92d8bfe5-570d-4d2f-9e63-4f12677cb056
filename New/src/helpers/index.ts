/**
 * Helper functions module exports
 * Provides centralized access to all helper utilities
 */

// Export data transformation utilities
export {
  transformCCPatientToAPContact,
  transformAPContactToCCPatient,
  extractCCCustomFieldValues,
  reduceCustomFieldValue,
  removeHtmlTags,
  formatDateToISO,
  calculateServiceAppointmentCounts,
  calculateServiceSpending,
  isValidEmail,
  isValidPhone,
  formatPhoneNumber,
  safeJsonParse,
  safeJsonStringify,
} from "./dataTransform";

// Export custom field utilities
export {
  getAPCustomFieldNameToIdMap,
  getCCCustomFieldIdToConfigMap,
  getOrCreateAPCustomFieldId,
  prepareAPCustomFieldUpdates,
  updateAPContactCustomFields,
  extractCCCustomFieldValues as extractCCPatientCustomFieldValues,
  clearCustomFieldCaches,
  refreshCustomFieldCaches,
  getCustomFieldCacheStats,
} from "./customFields";

// Re-export for convenience
export const helpers = {
  dataTransform: () => import("./dataTransform"),
  customFields: () => import("./customFields"),
};
