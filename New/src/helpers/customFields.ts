import type { APGetCustomFieldType, GetCCCustomField } from "@type";
import { apClient } from "@api/apClient";
import { ccClient } from "@api/ccClient";
import { db } from "@database";
import { apCustomFields, ccCustomFields } from "@database/schema";
import { eq } from "drizzle-orm";
import { logError } from "@utils/errorLogger";

/**
 * Custom field management utilities
 * Handles synchronization and mapping of custom fields between AP and CC
 */

/**
 * Cache for AP custom field name to ID mapping
 */
let apCustomFieldCache: Map<string, string> | null = null;

/**
 * Cache for CC custom field ID to config mapping
 */
let ccCustomFieldCache: Map<number, GetCCCustomField> | null = null;

/**
 * Gets AP custom field name to ID mapping
 * Uses caching to improve performance
 * 
 * @param forceRefresh - Force refresh of cache
 * @returns Map of field names to IDs
 */
export async function getAPCustomFieldNameToIdMap(forceRefresh: boolean = false): Promise<Map<string, string>> {
  if (!apCustomFieldCache || forceRefresh) {
    try {
      // Try to get from database first
      const dbFields = await db.select().from(apCustomFields);
      
      if (dbFields.length > 0) {
        apCustomFieldCache = new Map(
          dbFields.map(field => [field.name, field.apId!])
        );
      } else {
        // Fetch from API and cache in database
        const apiFields = await apClient.customField.all();
        apCustomFieldCache = new Map(
          apiFields.map(field => [field.name, field.id])
        );
        
        // Store in database for future use
        for (const field of apiFields) {
          await db.insert(apCustomFields)
            .values({
              apId: field.id,
              name: field.name,
              config: field,
            })
            .onConflictDoUpdate({
              target: apCustomFields.apId,
              set: {
                name: field.name,
                config: field,
                updatedAt: new Date(),
              },
            });
        }
      }
    } catch (error) {
      await logError(
        "AP_CUSTOM_FIELD_FETCH_ERROR",
        error,
        { forceRefresh },
        "CustomFieldHelper"
      );
      
      // Return empty map on error
      apCustomFieldCache = new Map();
    }
  }
  
  return apCustomFieldCache;
}

/**
 * Gets CC custom field ID to config mapping
 * Uses caching to improve performance
 * 
 * @param forceRefresh - Force refresh of cache
 * @returns Map of field IDs to configs
 */
export async function getCCCustomFieldIdToConfigMap(forceRefresh: boolean = false): Promise<Map<number, GetCCCustomField>> {
  if (!ccCustomFieldCache || forceRefresh) {
    try {
      // Try to get from database first
      const dbFields = await db.select().from(ccCustomFields);
      
      if (dbFields.length > 0) {
        ccCustomFieldCache = new Map(
          dbFields.map(field => [field.ccId!, field.config!])
        );
      } else {
        // Fetch from API and cache in database
        const apiFields = await ccClient.customField.all();
        ccCustomFieldCache = new Map(
          apiFields.map(field => [field.id, field])
        );
        
        // Store in database for future use
        for (const field of apiFields) {
          await db.insert(ccCustomFields)
            .values({
              ccId: field.id,
              name: field.name,
              config: field,
            })
            .onConflictDoUpdate({
              target: ccCustomFields.ccId,
              set: {
                name: field.name,
                config: field,
                updatedAt: new Date(),
              },
            });
        }
      }
    } catch (error) {
      await logError(
        "CC_CUSTOM_FIELD_FETCH_ERROR",
        error,
        { forceRefresh },
        "CustomFieldHelper"
      );
      
      // Return empty map on error
      ccCustomFieldCache = new Map();
    }
  }
  
  return ccCustomFieldCache;
}

/**
 * Gets AP custom field ID by name, creating it if it doesn't exist
 * 
 * @param fieldName - Name of the custom field
 * @param dataType - Data type for the field (default: TEXT)
 * @returns Custom field ID
 */
export async function getOrCreateAPCustomFieldId(
  fieldName: string,
  dataType: string = "TEXT"
): Promise<string> {
  const fieldMap = await getAPCustomFieldNameToIdMap();
  let fieldId = fieldMap.get(fieldName);
  
  if (!fieldId) {
    try {
      console.log(`Creating AP custom field: ${fieldName}`);
      const newField = await apClient.customField.create({
        name: fieldName,
        dataType,
      });
      
      fieldId = newField.id;
      
      // Update cache
      fieldMap.set(fieldName, fieldId);
      
      // Store in database
      await db.insert(apCustomFields)
        .values({
          apId: fieldId,
          name: fieldName,
          config: newField,
        })
        .onConflictDoUpdate({
          target: apCustomFields.apId,
          set: {
            name: fieldName,
            config: newField,
            updatedAt: new Date(),
          },
        });
      
    } catch (error) {
      await logError(
        "AP_CUSTOM_FIELD_CREATE_ERROR",
        error,
        { fieldName, dataType },
        "CustomFieldHelper"
      );
      throw error;
    }
  }
  
  return fieldId;
}

/**
 * Prepares custom field updates for AP contact
 * 
 * @param customFieldData - Object with field names and values
 * @returns Array of custom field updates with IDs
 */
export async function prepareAPCustomFieldUpdates(
  customFieldData: Record<string, string | number>
): Promise<Array<{ id: string; value: string }>> {
  const updates: Array<{ id: string; value: string }> = [];
  
  for (const [fieldName, value] of Object.entries(customFieldData)) {
    if (value !== null && value !== undefined && value !== "") {
      try {
        const fieldId = await getOrCreateAPCustomFieldId(fieldName);
        updates.push({
          id: fieldId,
          value: String(value),
        });
      } catch (error) {
        console.error(`Failed to prepare custom field update for ${fieldName}:`, error);
        // Continue with other fields even if one fails
      }
    }
  }
  
  return updates;
}

/**
 * Updates AP contact custom fields
 * 
 * @param apContactId - AP contact ID
 * @param customFieldData - Object with field names and values
 * @returns Success status
 */
export async function updateAPContactCustomFields(
  apContactId: string,
  customFieldData: Record<string, string | number>
): Promise<boolean> {
  try {
    const customFieldUpdates = await prepareAPCustomFieldUpdates(customFieldData);
    
    if (customFieldUpdates.length > 0) {
      await apClient.contact.update(apContactId, {
        customFields: customFieldUpdates,
      });
      
      console.log(`Updated ${customFieldUpdates.length} custom fields for AP contact: ${apContactId}`);
      return true;
    } else {
      console.log(`No custom fields to update for AP contact: ${apContactId}`);
      return true;
    }
    
  } catch (error) {
    await logError(
      "AP_CUSTOM_FIELD_UPDATE_ERROR",
      error,
      { apContactId, customFieldData },
      "CustomFieldHelper"
    );
    return false;
  }
}

/**
 * Extracts custom field values from CC patient custom fields
 * 
 * @param ccCustomFields - Array of CC patient custom fields
 * @returns Object with field labels and values
 */
export async function extractCCCustomFieldValues(
  ccCustomFields: any[]
): Promise<Record<string, string | number>> {
  const values: Record<string, string | number> = {};
  
  if (!ccCustomFields || ccCustomFields.length === 0) {
    return values;
  }
  
  const fieldConfigMap = await getCCCustomFieldIdToConfigMap();
  
  for (const customField of ccCustomFields) {
    try {
      const fieldConfig = fieldConfigMap.get(customField.field?.id || customField.field);
      
      if (fieldConfig && customField.values && customField.values.length > 0) {
        const fieldLabel = fieldConfig.label || fieldConfig.name;
        const fieldValue = customField.values[0]?.value;
        
        if (fieldValue !== null && fieldValue !== undefined && fieldValue !== "") {
          values[fieldLabel] = fieldValue;
        }
      }
    } catch (error) {
      console.error("Error extracting CC custom field value:", error);
      // Continue with other fields
    }
  }
  
  return values;
}

/**
 * Clears custom field caches
 * Useful for testing or when fields are updated externally
 */
export function clearCustomFieldCaches(): void {
  apCustomFieldCache = null;
  ccCustomFieldCache = null;
}

/**
 * Refreshes custom field caches
 * Forces a refresh of both AP and CC custom field caches
 */
export async function refreshCustomFieldCaches(): Promise<void> {
  await Promise.all([
    getAPCustomFieldNameToIdMap(true),
    getCCCustomFieldIdToConfigMap(true),
  ]);
}

/**
 * Gets custom field statistics
 * 
 * @returns Object with cache statistics
 */
export function getCustomFieldCacheStats(): {
  apFieldsCount: number;
  ccFieldsCount: number;
  apCacheLoaded: boolean;
  ccCacheLoaded: boolean;
} {
  return {
    apFieldsCount: apCustomFieldCache?.size || 0,
    ccFieldsCount: ccCustomFieldCache?.size || 0,
    apCacheLoaded: apCustomFieldCache !== null,
    ccCacheLoaded: ccCustomFieldCache !== null,
  };
}
