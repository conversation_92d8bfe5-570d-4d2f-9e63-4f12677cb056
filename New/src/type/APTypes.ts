export type GetAPContactType = {
  id: string;
  locationId: string;
  email?: string;
  phone?: string;
  name?: string;
  firstName?: string;
  lastName?: string;
  emailLowerCase?: string;
  timezone?: string;
  companyName?: string;
  dnd?: boolean;
  dndSettings?: {
    Call?: {
      status: string;
      message: string;
      code: string;
    };
    Email?: {
      status: string;
      message: string;
      code: string;
    };
    SMS?: {
      status: string;
      message: string;
      code: string;
    };
    WhatsApp?: {
      status: string;
      message: string;
      code: string;
    };
    GMB?: {
      status: string;
      message: string;
      code: string;
    };
    FB?: {
      status: string;
      message: string;
      code: string;
    };
  };
  type?: string;
  source?: string;
  assignedTo?: string;
  address1?: string;
  city?: string;
  state?: string;
  country?: string;
  postalCode?: string;
  website?: string;
  tags?: string[];
  dateOfBirth?: string;
  dateAdded?: string;
  dateUpdated?: string;
  attachments?: string;
  ssn?: string;
  gender?: string;
  keyword?: string;
  firstNameLowerCase?: string;
  fullNameLowerCase?: string;
  lastNameLowerCase?: string;
  lastActivity?: string;
  customFields?: { id: string; value: string }[];
  businessId?: string;
  attributionSource?: AttributionSourceType;
  lastAttributionSource?: AttributionSourceType;
};
export type PostAPContactType = {
  email?: string | null;
  phone?: string | null;
  name?: string | null;
  firstName?: string | null;
  lastName?: string | null;
  timezone?: string | null;
  dnd?: boolean;
  source?: string | null;
  assignedTo?: string | null;
  address1?: string | null;
  city?: string | null;
  state?: string | null;
  country?: string | null;
  postalCode?: string | null;
  tags?: string[];
  dateOfBirth?: string | null;
  ssn?: string | null;
  gender?: string | null;
  customFields?: { id: string; value: string | number }[];
};

export type GetAPAppointmentType = {
  calendarId: string;
  locationId: string;
  contactId: string;
  startTime: string;
  endTime: string;
  title: string | null;
  appointmentStatus?: string;
  appoinmentStatus?: string;
  assignedUserId?: string | null;
  address: string | null;
  id: string;
};

export type AttributionSourceType = {
  url?: string;
  campaign?: string;
  utmSource?: string;
  utmMedium?: string;
  utmContent?: string;
  referrer?: string;
  campaignId?: string;
  fbclid?: string;
  gclid?: string;
  msclikid?: string;
  dclid?: string;
  fbc?: string;
  fbp?: string;
  fbEventId?: string;
  userAgent?: string;
  ip?: string;
  medium?: string;
  mediumId?: string;
};

export type APGetCustomFieldType = {
  id: string;
  name: string;
  dataType: string;
  placeholder?: string;
  acceptedFormat?: string[];
  isMultipleFile?: boolean;
  maxNumberOfFiles?: number;
  textBoxListOptions?: {
    label: string;
    prefillValue: string;
  }[];
  position?: number;
};

export type APPostCustomfieldType = {
  name: string;
  dataType: string;
  placeholder?: string;
  acceptedFormat?: string[];
  isMultipleFile?: boolean;
  maxNumberOfFiles?: number;
  textBoxListOptions?: {
    label: string;
    prefillValue: string;
  }[];
  position?: number;
};

export type PostAPAppointmentType = {
  contactId: string;
  startTime: string;
  endTime?: string;
  title?: string;
  appointmentStatus?:
    | "new"
    | "confirmed"
    | "cancelled"
    | "showed"
    | "noshow"
    | "invalid";
  assignedUserId?: string;
  address?: string;
  ignoreDateRange?: boolean;
  toNotify?: boolean;
};

export type PutAPAppointmentType = {
  contactId?: string;
  startTime?: string;
  endTime?: string;
  title?: string;
  appointmentStatus?:
    | "new"
    | "confirmed"
    | "cancelled"
    | "showed"
    | "noshow"
    | "invalid";
  assignedUserId?: string;
  address?: string;
  ignoreDateRange?: boolean;
  toNotify?: boolean;
};

export type WebhookCalendar = {
  id: string;
  title: string;
  calendarName: string;
  selectedTimezone: string;
  appointmentId: string;
  startTime: string;
  endTime: string;
  status: string;
  appoinmentStatus: string;
  address: string;
  notes: string;
  date_created: string;
  created_by: string;
  created_by_user_id: string;
  created_by_meta: {
    source: string;
    channel: string;
  };
  last_updated_by_meta: {
    source: string;
    channel: string;
  };
};

export type GetAPNoteType = {
  id: string;
  body: string;
  userId: string;
  dateAdded: string;
  contactId: string;
};

export type UpdateAPCustomfields = {
  name: string;
  value: string | number;
};
